# 习惯小助手 - 微信小程序

一个帮助小朋友养成良好习惯的微信小程序，通过积分奖励机制让养成好习惯变得更有趣！

## 功能特性

### 🎯 核心功能
- **习惯管理**: 支持增删改习惯项，设置积分奖励/扣除
- **奖励系统**: 支持增删改奖励项，设置所需积分
- **积分机制**: 完成习惯获得积分，兑换奖励消耗积分
- **记录追踪**: 记录所有习惯完成和奖励兑换，支持取消操作

### 📋 模板系统
- **习惯模板**: 提供常见好习惯和坏习惯模板
- **奖励模板**: 提供适合儿童的奖励选项模板
- **一键添加**: 可直接使用模板快速创建习惯和奖励

### 📊 数据统计
- **积分统计**: 实时显示当前积分余额
- **完成统计**: 统计习惯完成次数和奖励兑换次数
- **历史记录**: 查看所有操作历史，支持筛选

## 技术架构

### 开发框架
- **微信小程序原生开发**
- **本地存储**: 使用 wx.Storage API 进行数据持久化
- **响应式设计**: 适配不同尺寸的手机屏幕

### 项目结构
```
├── app.js                 # 小程序入口文件
├── app.json               # 全局配置
├── app.wxss               # 全局样式
├── pages/                 # 页面目录
│   ├── index/             # 首页
│   ├── habits/            # 习惯管理页
│   ├── rewards/           # 奖励管理页
│   ├── records/           # 记录查看页
│   ├── settings/          # 设置页
│   ├── habit-detail/      # 习惯详情页
│   └── reward-detail/     # 奖励详情页
├── utils/                 # 工具函数
│   └── util.js            # 通用工具方法
├── images/                # 图片资源
└── sitemap.json           # 搜索配置
```

## 使用说明

### 1. 添加习惯
1. 进入"习惯"页面
2. 点击"添加新习惯"或使用模板
3. 设置习惯名称、类型（好习惯/坏习惯）和积分数量
4. 保存习惯

### 2. 添加奖励
1. 进入"奖励"页面  
2. 点击"添加新奖励"或使用模板
3. 设置奖励名称和所需积分
4. 保存奖励

### 3. 完成习惯
1. 在首页或习惯页面找到要完成的习惯
2. 点击"完成"按钮
3. 确认后获得相应积分

### 4. 兑换奖励
1. 在首页或奖励页面找到想要的奖励
2. 确保积分足够
3. 点击"兑换"按钮
4. 确认后消耗相应积分

### 5. 查看记录
1. 进入"记录"页面
2. 可以筛选查看习惯记录或奖励记录
3. 可以取消错误的记录

## 开发部署

### 环境要求
- 微信开发者工具
- 微信小程序开发账号

### 本地开发
1. 下载微信开发者工具
2. 导入项目目录
3. 配置小程序 AppID
4. 开始开发调试

### 图标资源
请参考 `images/README.md` 文件，添加所需的导航栏图标。

### 发布上线
1. 在微信开发者工具中点击"上传"
2. 在微信公众平台提交审核
3. 审核通过后发布

## 数据存储

### 本地存储结构
- `habits`: 习惯列表
- `rewards`: 奖励列表  
- `records`: 操作记录列表
- `totalPoints`: 当前积分总数
- `habitTemplates`: 习惯模板
- `rewardTemplates`: 奖励模板

### 数据备份
在设置页面可以导出所有数据到剪贴板，用于备份或迁移。

## 特色亮点

1. **儿童友好**: 界面简洁可爱，操作简单易懂
2. **积分激励**: 通过积分机制增强完成习惯的动力
3. **灵活配置**: 支持自定义习惯和奖励，适应不同需求
4. **模板丰富**: 提供常用的习惯和奖励模板
5. **记录完整**: 完整记录所有操作，支持撤销
6. **数据安全**: 本地存储，保护隐私

## 版本信息

- **当前版本**: v1.0.0
- **开发者**: 习惯小助手团队
- **更新时间**: 2024年

## 许可证

本项目仅供学习和个人使用。
