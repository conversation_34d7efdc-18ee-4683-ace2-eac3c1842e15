<!--pages/settings/settings.wxml-->
<view class="page" style="background: {{currentTheme.background || 'linear-gradient(135deg, #FFE4E6 0%, #FFF0F5 50%, #E8F5E8 100%)'}}">
  <!-- 积分显示 -->
  <view class="points-display" style="background: linear-gradient(135deg, {{currentTheme.primary || '#FF69B4'}} 0%, {{currentTheme.primaryDark || '#FF1493'}} 50%, {{currentTheme.primaryLight || '#FFB6C1'}} 100%)">
    <view class="points-icon">{{currentTheme.emoji || '🌟'}}</view>
    <view class="points-number">{{totalPoints}}</view>
    <view class="points-label">✨ 我的小星星 ✨</view>
  </view>

  <!-- 用户管理 -->
  <view class="card" style="background: {{currentTheme.cardBg || 'linear-gradient(145deg, #FFFFFF 0%, #FFF8FA 100%)'}}; border: 2rpx solid {{currentTheme.cardBorder || 'rgba(255, 182, 193, 0.3)'}};">
    <view class="section-title mb-20" style="color: {{currentTheme.primary || '#FF69B4'}};">👥 用户管理</view>

    <view class="setting-item current-user-item">
      <view class="setting-info">
        <view class="setting-name">当前用户</view>
        <view class="setting-desc">{{currentUser.avatar}} {{currentUser.name}} ({{totalPoints}}积分)</view>
      </view>
      <view class="user-actions">
        <button class="quick-switch-btn" style="background: {{currentTheme.primaryLight}}; color: {{currentTheme.primary}}" bindtap="quickSwitchUser" wx:if="{{usersCount > 1}}">
          切换
        </button>
        <view class="setting-arrow" bindtap="goToUsers">></view>
      </view>
    </view>

    <view class="setting-item" bindtap="goToUsers">
      <view class="setting-info">
        <view class="setting-name">用户管理</view>
        <view class="setting-desc">管理所有用户 (共{{usersCount}}个用户)</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
  </view>

  <!-- 个性化设置 -->
  <view class="card" style="background: {{currentTheme.cardBg || 'linear-gradient(145deg, #FFFFFF 0%, #FFF8FA 100%)'}}; border: 2rpx solid {{currentTheme.cardBorder || 'rgba(255, 182, 193, 0.3)'}};">
    <view class="section-title mb-20" style="color: {{currentTheme.primary || '#FF69B4'}};">🎨 个性化设置</view>

    <view class="setting-item" bindtap="goToTheme">
      <view class="setting-info">
        <view class="setting-name">主题色彩</view>
        <view class="setting-desc">当前：{{currentTheme.name || '💖 粉色公主'}}</view>
      </view>
      <view class="theme-preview-mini">
        <view class="mini-color" style="background: {{currentTheme.primary || '#FF69B4'}}"></view>
        <view class="setting-arrow">></view>
      </view>
    </view>
  </view>

  <!-- 数据管理 -->
  <view class="card" style="background: {{currentTheme.cardBg || 'linear-gradient(145deg, #FFFFFF 0%, #FFF8FA 100%)'}}; border: 2rpx solid {{currentTheme.cardBorder || 'rgba(255, 182, 193, 0.3)'}};">
    <view class="section-title mb-20" style="color: {{currentTheme.primary || '#FF69B4'}};">📊 数据管理</view>
    
    <view class="setting-item" bindtap="resetPoints">
      <view class="setting-info">
        <view class="setting-name">重置积分</view>
        <view class="setting-desc">将当前积分重置为0</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
    
    <view class="setting-item" bindtap="exportData">
      <view class="setting-info">
        <view class="setting-name">导出数据</view>
        <view class="setting-desc">将数据复制到剪贴板</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
    
    <view class="setting-item danger" bindtap="clearAllData">
      <view class="setting-info">
        <view class="setting-name">清空所有数据</view>
        <view class="setting-desc">删除所有习惯、奖励和记录</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
  </view>

  <!-- 帮助与支持 -->
  <view class="card" style="background: {{currentTheme.cardBg || 'linear-gradient(145deg, #FFFFFF 0%, #FFF8FA 100%)'}}; border: 2rpx solid {{currentTheme.cardBorder || 'rgba(255, 182, 193, 0.3)'}};">
    <view class="section-title mb-20" style="color: {{currentTheme.primary || '#FF69B4'}};">💡 帮助与支持</view>
    
    <view class="setting-item" bindtap="showHelp">
      <view class="setting-info">
        <view class="setting-name">使用帮助</view>
        <view class="setting-desc">了解如何使用这个应用</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
    
    <view class="setting-item" bindtap="showAbout">
      <view class="setting-info">
        <view class="setting-name">关于应用</view>
        <view class="setting-desc">版本信息和应用介绍</view>
      </view>
      <view class="setting-arrow">></view>
    </view>
  </view>

  <!-- 应用信息 -->
  <view class="card" style="background: {{currentTheme.cardBg || 'linear-gradient(145deg, #FFFFFF 0%, #FFF8FA 100%)'}}; border: 2rpx solid {{currentTheme.cardBorder || 'rgba(255, 182, 193, 0.3)'}};">
    <view class="section-title mb-20" style="color: {{currentTheme.primary || '#FF69B4'}};">ℹ️ 应用信息</view>
    
    <view class="info-item">
      <view class="info-label">版本号</view>
      <view class="info-value">{{version}}</view>
    </view>
    
    <view class="info-item">
      <view class="info-label">开发者</view>
      <view class="info-value">习惯小助手团队</view>
    </view>
  </view>

  <!-- 底部说明 -->
  <view class="footer-text">
    <text>让养成好习惯变得更有趣！</text>
  </view>
</view>
