/* pages/data-sync/data-sync.wxss */

.data-sync-page {
  min-height: 100vh;
  transition: background 0.3s ease;
  padding-bottom: 120rpx; /* 为底部按钮留空间 */
}

/* 页面标题 */
.page-title {
  text-align: center;
  margin-bottom: 20rpx;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.page-subtitle {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 区块标题 */
.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.select-all-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid currentColor;
}

/* 当前用户 */
.current-user {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
  border-radius: 16rpx;
  border: 2rpx solid #28a745;
}

.current-user .user-avatar {
  font-size: 50rpx;
  margin-right: 20rpx;
}

.current-user .user-info {
  flex: 1;
}

.current-user .user-name {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 5rpx;
}

.current-user .user-desc {
  font-size: 24rpx;
  color: #28a745;
}

/* 用户列表 */
.users-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.user-item.selected {
  border-color: currentColor;
  box-shadow: 0 4rpx 15rpx rgba(255, 105, 180, 0.2);
}

.user-item .user-avatar {
  font-size: 45rpx;
  margin-right: 20rpx;
}

.user-item .user-info {
  flex: 1;
}

.user-item .user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.user-item .user-stats {
  font-size: 22rpx;
  color: #666;
}

.select-indicator {
  font-size: 30rpx;
  color: #28a745;
  font-weight: bold;
}

/* 同步类型选择 */
.sync-type-tabs {
  display: flex;
  gap: 15rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 15rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* 数据列表 */
.data-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid transparent;
  transition: all 0.3s ease;
}

.data-item.selected {
  background: rgba(255, 105, 180, 0.05);
  border-color: currentColor;
}

.data-info {
  flex: 1;
}

.data-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.data-points {
  font-size: 24rpx;
  font-weight: 600;
}

.select-checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid currentColor;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: white;
  transition: all 0.3s ease;
}

.select-checkbox.checked {
  color: white;
}

/* 空状态 */
.empty-hint {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
  font-size: 26rpx;
}

/* 同步按钮 */
.sync-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background: white;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.sync-btn {
  width: 100%;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  padding: 20rpx;
  border-radius: 16rpx;
  border: none;
}
